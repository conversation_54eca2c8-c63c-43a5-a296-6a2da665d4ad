/**
 * GameSprite Engine - PIXI 渲染器
 */

import * as PIXI from 'pixi.js';
import type { Renderer } from './types.js';

export class <PERSON><PERSON><PERSON><PERSON><PERSON> implements Renderer {
  private app: PIXI.Application;

  constructor() {
    this.app = new PIXI.Application();
  }

  async initialize(canvas: HTMLCanvasElement, options?: {
    width?: number;
    height?: number;
    backgroundColor?: number;
  }): Promise<void> {
    // 确保 Canvas 样式为 100%
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.display = 'block';

    await this.app.init({
      canvas: canvas,
      resizeTo: canvas.parentElement || canvas, // 让 PIXI 自动监听容器大小变化
      background: options?.backgroundColor || 0x2a2a2a,
      antialias: true,
      resolution: window.devicePixelRatio || 1,
    });

    console.log(`🎨 PIXI 初始化完成，自动监听容器大小变化`);
    console.log(`📐 初始大小: ${this.app.screen.width}x${this.app.screen.height}`);
  }

  get width(): number {
    return this.app.screen.width;
  }

  get height(): number {
    return this.app.screen.height;
  }

  clear(): void {
    // PIXI 会自动清除
  }

  render(object: any): void {
    // PIXI 会自动渲染舞台
  }

  getStage(): PIXI.Container {
    return this.app.stage;
  }

  getApp(): PIXI.Application {
    return this.app;
  }

  destroy(): void {
    this.app.destroy(true);
  }
}

/**
 * GameSprite Engine - 简化版核心引擎
 */

import type { EngineOptions } from './types.js';
import { generateId } from './types.js';
import { PixiRenderer } from './PixiRenderer.js';
import { Scene } from './Scene.js';
import * as PIXI from 'pixi.js';

export class GameSpriteEngine {
  readonly id: string;

  // 渲染器
  private renderer: PixiRenderer | null = null;

  // 当前场景
  private currentScene: Scene | null = null;

  // 视口边框
  private viewportBorder: PIXI.Graphics | null = null;

  // 项目设置（视口大小）
  private _projectWidth: number = 800;
  private _projectHeight: number = 600;
  private _backgroundColor: number = 0x2a2a2a;

  // 舞台设置（舞台:视口 = 3:2）
  private _stageWidth: number = 1200;  // 项目宽度 * 1.5
  private _stageHeight: number = 900;  // 项目高度 * 1.5

  // 视图变换
  private _zoom: number = 1.0;
  private _pan: { x: number; y: number } = { x: 0, y: 0 };
  private _rotation: number = 0;

  // 引擎状态
  private initialized: boolean = false;
  private running: boolean = false;
  private lastUpdateTime: number = 0;

  constructor() {
    this.id = generateId();
  }

  // === 初始化 ===
  async initialize(canvas: HTMLCanvasElement, options?: EngineOptions): Promise<void> {
    if (this.initialized) {
      throw new Error('Engine already initialized');
    }

    try {
      // 设置项目参数（视口大小）
      this._projectWidth = options?.width || 800;
      this._projectHeight = options?.height || 600;
      this._backgroundColor = options?.backgroundColor || 0x2a2a2a;

      // 计算舞台大小（舞台:视口 = 3:2）
      this._stageWidth = this._projectWidth * 1.5;
      this._stageHeight = this._projectHeight * 1.5;

      console.log(`📐 项目大小（视口）: ${this._projectWidth}x${this._projectHeight}`);
      console.log(`🎭 舞台大小: ${this._stageWidth}x${this._stageHeight}`);

      // 创建渲染器，让 PIXI 自动处理 Canvas 大小
      this.renderer = new PixiRenderer();
      await this.renderer.initialize(canvas, {
        backgroundColor: this._backgroundColor
      });

      // 设置 PIXI 自动 resize 监听
      this.setupAutoResize();

      // 初始舞台变换
      this.updateStageTransform();

      // 创建默认场景
      this.currentScene = new Scene(this._projectWidth, this._projectHeight, 'DefaultScene');

      // 添加测试对象
      const testRect = new PIXI.Graphics().rect(0, 5, 100, 100).stroke({ color: 0x000000, width: 2 }).fill({ color: 0xff0000 });
      this.currentScene.getPixiObject().addChild(testRect);

      // 将场景添加到舞台
      this.renderer.getStage().addChild(this.currentScene.getPixiObject());

      // 创建视口边框
      this.createViewportBorder();

      // 启动更新循环
      this.startUpdateLoop();

      this.initialized = true;
      console.log('✅ GameSprite Engine: 初始化完成');

    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to initialize engine: ${message}`);
    }
  }

  // === 场景管理 ===
  setScene(scene: Scene): void {
    if (this.currentScene) {
      this.renderer?.getStage().removeChild(this.currentScene.getPixiObject());
    }

    this.currentScene = scene;

    if (this.renderer) {
      this.renderer.getStage().addChild(scene.getPixiObject());
    }
  }

  getScene(): Scene | null {
    return this.currentScene;
  }

  createScene(width?: number, height?: number, name?: string): Scene {
    const scene = new Scene(
      width || this.renderer?.width || 800,
      height || this.renderer?.height || 600,
      name
    );
    return scene;
  }

  // === 渲染循环 ===
  private startUpdateLoop(): void {
    if (this.running) return;

    this.running = true;
    this.lastUpdateTime = performance.now();

    const update = (currentTime: number) => {
      if (!this.running) return;

      const deltaTime = currentTime - this.lastUpdateTime;
      this.lastUpdateTime = currentTime;

      // 更新场景
      if (this.currentScene) {
        this.currentScene.update(deltaTime);
      }

      // 继续循环
      requestAnimationFrame(update);
    };

    requestAnimationFrame(update);
  }

  // === 项目属性 ===
  get projectWidth(): number {
    return this._projectWidth;
  }

  get projectHeight(): number {
    return this._projectHeight;
  }

  get backgroundColor(): number {
    return this._backgroundColor;
  }

  // === 舞台属性 ===
  get stageWidth(): number {
    return this._stageWidth;
  }

  get stageHeight(): number {
    return this._stageHeight;
  }

  // === 视图变换 ===
  get zoom(): number {
    return this._zoom;
  }

  set zoom(value: number) {
    this._zoom = value;
    this.updateViewTransform();
  }

  get pan(): { x: number; y: number } {
    return { ...this._pan };
  }

  set pan(value: { x: number; y: number }) {
    this._pan = { ...value };
    this.updateViewTransform();
  }

  get rotation(): number {
    return this._rotation;
  }

  set rotation(value: number) {
    this._rotation = value;
    this.updateViewTransform();
  }

  // === 视图控制 ===
  recenter(): void {
    this._pan = { x: 0, y: 0 };
    this._zoom = 1.0;
    this._rotation = 0;
    this.updateViewTransform();
  }

  private updateViewTransform(): void {
    if (!this.renderer) return;

    // 重新计算舞台变换，包含用户的视图变换
    const app = this.renderer.getApp();
    const canvasWidth = app.screen.width;
    const canvasHeight = app.screen.height;

    // 基础舞台缩放
    const scaleX = canvasWidth / this._stageWidth;
    const scaleY = canvasHeight / this._stageHeight;
    const baseStageScale = Math.min(scaleX, scaleY);

    // 应用用户缩放
    const finalScale = baseStageScale * this._zoom;

    // 基础舞台位置
    const stageDisplayWidth = this._stageWidth * baseStageScale;
    const stageDisplayHeight = this._stageHeight * baseStageScale;
    const stageLeft = (canvasWidth - stageDisplayWidth) / 2;
    const stageTop = (canvasHeight - stageDisplayHeight) / 2;

    // 视口在舞台中的偏移
    const viewportOffsetX = (this._stageWidth - this._projectWidth) / 2;
    const viewportOffsetY = (this._stageHeight - this._projectHeight) / 2;

    // 基础视口位置
    const baseViewportX = stageLeft + viewportOffsetX * baseStageScale;
    const baseViewportY = stageTop + viewportOffsetY * baseStageScale;

    // 应用用户平移
    const finalX = baseViewportX + this._pan.x * baseStageScale;
    const finalY = baseViewportY + this._pan.y * baseStageScale;

    // 应用变换到 PIXI Stage
    const stage = this.renderer.getStage();
    stage.scale.set(finalScale);
    stage.position.set(finalX, finalY);
    stage.rotation = this._rotation;
  }

  // === 视口边框 ===
  private createViewportBorder(): void {
    if (!this.renderer) return;

    this.viewportBorder = new PIXI.Graphics();
    this.updateViewportBorder();

    // 添加到舞台，确保在最上层
    this.renderer.getStage().addChild(this.viewportBorder);

    console.log('🔲 视口边框已创建');
  }

  private updateViewportBorder(): void {
    if (!this.viewportBorder) return;

    this.viewportBorder.clear();
    // 视口边框：(0,0) 到 (projectWidth, projectHeight)
    this.viewportBorder.rect(
      0,                    // 左边界 = 0
      0,                    // 上边界 = 0
      this._projectWidth,   // 宽度
      this._projectHeight   // 高度
    );
    this.viewportBorder.stroke({
      color: 0xff0000,  // 红色边框
      width: 4,         // 边框粗细
      alignment: 0      // 内边框
    });
  }

  showViewportBorder(): void {
    if (this.viewportBorder) {
      this.viewportBorder.visible = true;
    }
  }

  hideViewportBorder(): void {
    if (this.viewportBorder) {
      this.viewportBorder.visible = false;
    }
  }

  setViewportBorderColor(color: number): void {
    if (this.viewportBorder) {
      this.viewportBorder.clear();
      this.viewportBorder.rect(
        0,
        0,
        this._projectWidth,
        this._projectHeight
      );
      this.viewportBorder.stroke({
        color: color,
        width: 4,
        alignment: 0
      });
    }
  }

  // === PIXI 自动 resize 监听 ===
  private setupAutoResize(): void {
    if (!this.renderer) return;

    const app = this.renderer.getApp();

    // 监听 PIXI 的 resize 事件
    app.renderer.on('resize', (width: number, height: number) => {
      console.log(`🎨 PIXI 自动 resize: ${width}x${height}`);
      this.updateStageTransform();
    });

    console.log(`👂 PIXI 自动 resize 监听已设置`);
  }

  // === 舞台变换 ===
  private updateStageTransform(): void {
    if (!this.renderer) return;

    const app = this.renderer.getApp();
    const canvasWidth = app.screen.width;
    const canvasHeight = app.screen.height;
    if (!this.renderer) return;

    console.log(`🎭 开始舞台变换计算:`);
    console.log(`   Canvas: ${canvasWidth}x${canvasHeight}`);
    console.log(`   舞台: ${this._stageWidth}x${this._stageHeight}`);
    console.log(`   视口: ${this._projectWidth}x${this._projectHeight}`);

    // 计算舞台在 Canvas 中的缩放比例（等比缩放）
    const scaleX = canvasWidth / this._stageWidth;
    const scaleY = canvasHeight / this._stageHeight;
    const stageScale = Math.min(scaleX, scaleY);

    console.log(`   缩放计算: scaleX=${scaleX.toFixed(3)}, scaleY=${scaleY.toFixed(3)}, final=${stageScale.toFixed(3)}`);

    // 检查缩放值是否有效
    if (stageScale <= 0 || !isFinite(stageScale)) {
      console.error(`❌ 无效的缩放值: ${stageScale}`);
      return;
    }

    // 计算舞台在 Canvas 中的位置（居中）
    const stageDisplayWidth = this._stageWidth * stageScale;
    const stageDisplayHeight = this._stageHeight * stageScale;

    // 舞台左上角在 Canvas 中的位置
    const stageLeft = (canvasWidth - stageDisplayWidth) / 2;
    const stageTop = (canvasHeight - stageDisplayHeight) / 2;

    // 视口在舞台中的偏移（视口居中在舞台中）
    const viewportOffsetX = (this._stageWidth - this._projectWidth) / 2;
    const viewportOffsetY = (this._stageHeight - this._projectHeight) / 2;

    // 视口左上角在 Canvas 中的最终位置
    const viewportX = stageLeft + viewportOffsetX * stageScale;
    const viewportY = stageTop + viewportOffsetY * stageScale;

    console.log(`   最终位置: (${viewportX.toFixed(1)}, ${viewportY.toFixed(1)})`);

    // 应用变换到 PIXI Stage（让视口的 (0,0) 对应到正确位置）
    const stage = this.renderer.getStage();
    stage.scale.set(stageScale);
    stage.position.set(viewportX, viewportY);

    console.log(`✅ 舞台变换完成: 缩放 ${stageScale.toFixed(2)}x, 视口位置 (${viewportX.toFixed(1)}, ${viewportY.toFixed(1)})`);
  }

  // === Canvas 大小变化处理 ===
  resize(newWidth: number, newHeight: number): void {
    if (!this.renderer || !this.initialized) return;

    // 检查新尺寸是否有效
    if (newWidth <= 0 || newHeight <= 0) {
      console.warn(`⚠️ 无效的 Canvas 尺寸: ${newWidth}x${newHeight}`);
      return;
    }

    console.log(`📐 Canvas 大小变化: ${newWidth}x${newHeight}`);

    try {
      const app = this.renderer.getApp();
      const canvas = app.canvas as HTMLCanvasElement;

      console.log(`🔍 Resize 前状态:`);
      console.log(`   Canvas DOM: ${canvas.clientWidth}x${canvas.clientHeight}`);
      console.log(`   Canvas 内部: ${canvas.width}x${canvas.height}`);
      console.log(`   PIXI Screen: ${app.screen.width}x${app.screen.height}`);

      // PIXI 8.x 的正确 resize 方法
      app.renderer.resize(newWidth, newHeight);

      console.log(`🔍 Resize 后状态:`);
      console.log(`   Canvas DOM: ${canvas.clientWidth}x${canvas.clientHeight}`);
      console.log(`   Canvas 内部: ${canvas.width}x${canvas.height}`);
      console.log(`   PIXI Screen: ${app.screen.width}x${app.screen.height}`);

      // 重新计算舞台变换
      this.updateStageTransform();

      console.log(`✅ Canvas 大小调整完成`);

    } catch (error) {
      console.error('❌ Canvas 大小调整失败:', error);
      console.error(error);
    }
  }

  // === 工具方法 ===
  getRenderer(): PixiRenderer | null {
    return this.renderer;
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  isRunning(): boolean {
    return this.running;
  }

  // === 清理 ===
  destroy(): void {
    this.running = false;

    if (this.currentScene) {
      this.currentScene.destroy();
      this.currentScene = null;
    }

    if (this.viewportBorder) {
      this.viewportBorder.destroy();
      this.viewportBorder = null;
    }

    if (this.renderer) {
      this.renderer.destroy();
      this.renderer = null;
    }

    this.initialized = false;
    console.log('🧹 GameSprite Engine: 引擎已销毁');
  }
}

<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { createEngine, type GameSpriteEngine, type Scene } from '../engine/index.js';
  import * as PIXI from 'pixi.js';

  // 引擎实例
  let engine: GameSpriteEngine | null = null;
  let scene: Scene | null = null;

  // Canvas 元素
  let canvasElement: HTMLCanvasElement;

  // 状态
  let isInitialized = false;
  let initError: string | null = null;

  // 添加测试对象来验证视口边框
  function addTestObjects(engine: GameSpriteEngine, scene: Scene | null) {
    if (!scene) return;

    try {
      console.log('🎨 添加测试对象...');

      // 在视口中心添加一个绿色方块
      const centerRect = new PIXI.Graphics();
      centerRect.rect(-25, -25, 50, 50);
      centerRect.fill({ color: 0x00ff00 });
      centerRect.x = engine.projectWidth / 2;   // 视口中心 X
      centerRect.y = engine.projectHeight / 2;  // 视口中心 Y
      scene.getPixiObject().addChild(centerRect);

      // 在视口左上角添加一个蓝色方块
      const topLeftRect = new PIXI.Graphics();
      topLeftRect.rect(0, 0, 100, 100);
      topLeftRect.fill({ color: 0x0000ff });
      topLeftRect.x = 50;   // 距离视口左边界 50px
      topLeftRect.y = 50;   // 距离视口上边界 50px
      scene.getPixiObject().addChild(topLeftRect);

      // 在视口右下角添加一个黄色方块
      const bottomRightRect = new PIXI.Graphics();
      bottomRightRect.rect(0, 0, 100, 100);
      bottomRightRect.fill({ color: 0xffff00 });
      bottomRightRect.x = engine.projectWidth - 150;   // 距离视口右边界 150px
      bottomRightRect.y = engine.projectHeight - 150;  // 距离视口下边界 150px
      scene.getPixiObject().addChild(bottomRightRect);

      // 在视口外添加一个红色方块（左边）
      const outsideLeftRect = new PIXI.Graphics();
      outsideLeftRect.rect(0, 0, 100, 100);
      outsideLeftRect.fill({ color: 0xff0000 });
      outsideLeftRect.x = -150;  // 视口左边界外
      outsideLeftRect.y = 200;
      scene.getPixiObject().addChild(outsideLeftRect);

      // 在视口外添加一个紫色方块（右边）
      const outsideRightRect = new PIXI.Graphics();
      outsideRightRect.rect(0, 0, 100, 100);
      outsideRightRect.fill({ color: 0xff00ff });
      outsideRightRect.x = engine.projectWidth + 50;  // 视口右边界外
      outsideRightRect.y = 200;
      scene.getPixiObject().addChild(outsideRightRect);

      console.log('✅ 测试对象添加完成:', {
        centerRect: '绿色 - 视口中心',
        topLeftRect: '蓝色 - 视口左上角',
        bottomRightRect: '黄色 - 视口右下角',
        outsideLeftRect: '红色 - 视口外左边',
        outsideRightRect: '紫色 - 视口外右边'
      });

    } catch (error) {
      console.error('❌ 添加测试对象失败:', error);
    }
  }

  // 初始化引擎
  async function initializeEngine() {
    try {
      console.log('🎮 GameSprite Engine: 开始初始化...');

      // 创建引擎实例（项目大小 = 视口大小）
      const newEngine = await createEngine(canvasElement, {
        width: 1920,  // 项目宽度（视口宽度）
        height: 1080, // 项目高度（视口高度）
        backgroundColor: 0x2a2a2a
      });

      // 获取默认场景
      const newScene = newEngine.getScene();

      console.log('✅ GameSprite Engine: 引擎创建成功', {
        engineId: newEngine.id,
        sceneId: newScene?.id,
        renderer: newEngine.getRenderer()
      });

      // 添加测试对象来验证视口边框
      addTestObjects(newEngine, newScene);

      // 赋值给组件变量
      engine = newEngine;
      scene = newScene;
      isInitialized = true;

    } catch (error) {
      console.error('❌ GameSprite Engine: 初始化失败', error);
      initError = error instanceof Error ? error.message : '初始化失败';
    }
  }

  // PIXI 会自动处理 Canvas 大小变化，不需要手动监听

  // 组件挂载时初始化
  onMount(async () => {
    if (canvasElement) {
      await initializeEngine();
    }
  });

  // 组件销毁时清理
  onDestroy(() => {
    if (engine) {
      console.log('🧹 GameSprite Engine: 清理引擎');
      engine.destroy();
      engine = null;
      scene = null;
    }
  });

  // 暴露给父组件的方法
  export function getEngine(): GameSpriteEngine | null {
    return engine;
  }

  export function getScene(): Scene | null {
    return scene;
  }

  export function isEngineReady(): boolean {
    return isInitialized && engine !== null;
  }
</script>

<div class="center-panel">
  <!-- 引擎状态显示 -->
  <div class="engine-status">
    {#if initError}
      <div class="status-error">
        ❌ 引擎初始化失败: {initError}
      </div>
    {:else if !isInitialized}
      <div class="status-loading">
        🔄 正在初始化 GameSprite Engine...
      </div>
    {:else}
      <div class="status-ready">
        ✅ GameSprite Engine 已就绪
      </div>
    {/if}
  </div>

  <!-- 主画布 -->
  <div class="canvas-container">
    <canvas
      bind:this={canvasElement}
      class="main-canvas"
      class:initialized={isInitialized}
      class:error={!!initError}
    ></canvas>
  </div>

  <!-- 调试信息 -->
  {#if isInitialized && engine}
    <div class="debug-info">
      <div class="debug-item">
        <span class="debug-label">引擎ID:</span>
        <span class="debug-value">{engine.id}</span>
      </div>
      {#if scene && engine}
        <div class="debug-item">
          <span class="debug-label">场景:</span>
          <span class="debug-value">{scene.name}</span>
        </div>
        <div class="debug-item">
          <span class="debug-label">项目大小:</span>
          <span class="debug-value">{engine.projectWidth}x{engine.projectHeight}</span>
        </div>
        <div class="debug-item">
          <span class="debug-label">舞台大小:</span>
          <span class="debug-value">{engine.stageWidth}x{engine.stageHeight}</span>
        </div>
        <div class="debug-item">
          <span class="debug-label">缩放:</span>
          <span class="debug-value">{engine.zoom.toFixed(2)}x</span>
        </div>
        <div class="debug-item">
          <span class="debug-label">平移:</span>
          <span class="debug-value">({engine.pan.x.toFixed(0)}, {engine.pan.y.toFixed(0)})</span>
        </div>
      {/if}
      <div class="debug-item">
        <span class="debug-label">状态:</span>
        <span class="debug-value">{engine.isRunning() ? '运行中' : '已停止'}</span>
      </div>
    </div>
  {/if}
</div>

<style>
  .center-panel {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background: var(--theme-bg-secondary, #1e1e1e);
    position: relative;
    overflow: hidden;
  }

  .engine-status {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .status-loading {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
  }

  .status-ready {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
  }

  .status-error {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.3);
  }

  .canvas-container {
    flex: 1;
    width: 100%;
    height: 100%;
    position: relative;
  }

  .main-canvas {
    width: 100%;
    height: 100%;
    border: none;
    background: #2a2a2a;
    transition: opacity 0.3s ease;
    display: block;
  }

  .main-canvas:not(.initialized) {
    opacity: 0.5;
  }

  .main-canvas.error {
    border-color: #dc3545;
    opacity: 0.3;
  }

  .debug-info {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-family: 'Courier New', monospace;
    z-index: 10;
  }

  .debug-item {
    display: flex;
    margin-bottom: 2px;
  }

  .debug-item:last-child {
    margin-bottom: 0;
  }

  .debug-label {
    color: #888;
    margin-right: 8px;
    min-width: 60px;
  }

  .debug-value {
    color: #fff;
    font-weight: 500;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .engine-status,
    .debug-info {
      font-size: 0.75rem;
      padding: 6px 8px;
    }

    .debug-info {
      position: relative;
      margin-top: 10px;
    }
  }
</style>
